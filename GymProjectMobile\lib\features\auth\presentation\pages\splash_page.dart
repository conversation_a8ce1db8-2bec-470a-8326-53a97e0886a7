/// Splash Page - GymKod Pro Mobile
///
/// Bu sayfa Angular frontend'deki loading state'inden uyarlanmıştır.
/// Referans: Angular app component'deki initial loading
///
/// RESPONSIVE DESIGN ENHANCEMENT:
/// Bu sayfa artık responsive tasarım desteği içerir.
/// Angular frontend'deki responsive splash pattern'leri u<PERSON>tır.
library;

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/widgets.dart';
import '../providers/auth_provider.dart';

/// Splash Page
/// Angular frontend'deki app loading'e benzer
class SplashPage extends ConsumerStatefulWidget {
  const SplashPage({super.key});

  @override
  ConsumerState<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends ConsumerState<SplashPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Animation setup (Angular'daki fade-in effect'e benzer)
    _animationController = AnimationController(
      duration: AppConstants.normalAnimation,
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();

    LoggingService.widgetLog('SplashPage', 'initState');

    // Auth durumunu kontrol et ve yönlendir
    _checkAuthAndNavigate();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Auth durumunu kontrol et ve uygun sayfaya yönlendir - CRITICAL FIX
  Future<void> _checkAuthAndNavigate() async {
    try {
      LoggingService.authLog('Splash: Auth check started');

      if (!mounted) return;

      // Auth state'i kontrol et
      final authState = ref.read(authProvider);

      LoggingService.authLog('Splash: Auth state checked',
        details: 'Initialized: ${authState.isInitialized}, Authenticated: ${authState.isAuthenticated}');

      if (authState.isInitialized) {
        // Minimum animasyon süresi için kısa bir bekleme (UX için)
        await Future.delayed(const Duration(milliseconds: 800));

        if (!mounted) return;

        if (authState.isAuthenticated && authState.user != null) {
          // Kullanıcı giriş yapmış, role'e göre yönlendir
          final userRole = authState.user?.role.toLowerCase();
          final redirectRoute = _getDefaultRouteForRole(userRole);

          LoggingService.navigationLog('Splash redirect', redirectRoute,
            details: 'User authenticated: ${authState.user?.name}, Role: $userRole');
          context.go(redirectRoute);
        } else {
          // Kullanıcı giriş yapmamış, login sayfasına yönlendir
          LoggingService.navigationLog('Splash redirect', '/auth/login',
            details: 'User not authenticated');
          context.go('/auth/login');
        }
      } else {
        // Auth henüz initialize olmamış, maksimum 5 saniye bekle
        int attempts = 0;
        const maxAttempts = 25; // 25 * 200ms = 5 saniye

        while (!authState.isInitialized && attempts < maxAttempts && mounted) {
          await Future.delayed(const Duration(milliseconds: 200));
          final currentState = ref.read(authProvider);

          if (currentState.isInitialized) {
            // Initialize oldu, tekrar kontrol et
            return _checkAuthAndNavigate();
          }

          attempts++;
        }

        // Timeout durumunda login sayfasına yönlendir
        if (mounted && !ref.read(authProvider).isInitialized) {
          LoggingService.authLog('Splash: Auth initialization timeout, redirecting to login');
          context.go('/auth/login');
        }
      }

    } catch (e, stackTrace) {
      LoggingService.logException(e, stackTrace, context: 'SplashPage Auth Check');

      // Hata durumunda login sayfasına yönlendir
      if (mounted) {
        context.go('/auth/login');
      }
    }
  }

  /// Role'e göre varsayılan route'u döndür
  String _getDefaultRouteForRole(String? role) {
    switch (role?.toLowerCase()) {
      case 'member':
        return AppRoutes.memberMain;
      case 'admin':
      case 'owner':
        return AppRoutes.home;
      default:
        return AppRoutes.home;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final authState = ref.watch(authProvider);

    // Responsive değerleri önceden hesapla (performans optimizasyonu)
    final logoSize = _getResponsiveLogoSize(context);
    final iconSize = _getResponsiveIconSize(context);
    final blurRadius = _getResponsiveBlurRadius(context);
    final shadowOffset = _getResponsiveShadowOffset(context);
    final sectionSpacing = _getResponsiveSpacing(context, 'section');
    final smallSpacing = _getResponsiveSpacing(context, 'small');
    final horizontalPadding = _getResponsiveHorizontalPadding(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Container(
        decoration: BoxDecoration(
          // Angular login/register background gradient'ı
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: theme.brightness == Brightness.light
                ? AppColors.backgroundGradient
                : AppColors.darkBackgroundGradient,
          ),
        ),
        child: SafeArea(
          child: Center(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Logo Container (Angular login component'deki logo-container'a benzer) - Optimize edildi
                        Container(
                          width: logoSize,
                          height: logoSize,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: AppColors.primaryGradient,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                                blurRadius: blurRadius,
                                offset: Offset(0, shadowOffset),
                              ),
                            ],
                          ),
                          child: Icon(
                            Icons.fitness_center,
                            size: iconSize,
                            color: theme.colorScheme.onPrimary,
                          ),
                        ),

                        SizedBox(height: sectionSpacing),

                        // App Title (Angular branding'e benzer) - Optimize edildi
                        Text(
                          AppConstants.appName,
                          style: _getResponsiveTitleStyle(context, theme),
                        ),

                        SizedBox(height: smallSpacing),

                        // App Description - Optimize edildi
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
                          child: Text(
                            AppConstants.appDescription,
                            style: _getResponsiveBodyStyle(context, theme),
                            textAlign: TextAlign.center,
                          ),
                        ),

                        SizedBox(height: sectionSpacing),

                        // Loading Indicator (Angular loading-spinner'a benzer)
                        const LoadingSpinner(
                          size: LoadingSpinnerSize.medium,
                          showText: true,
                          text: 'Başlatılıyor...',
                        ),

                        SizedBox(height: sectionSpacing),

                        // Auth Status Text (Debug için)
                        if (kDebugMode) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: AppSpacing.paddingMD,
                              vertical: AppSpacing.paddingSM,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                              borderRadius: BorderRadius.circular(AppSpacing.radiusSM),
                            ),
                            child: Column(
                              children: [
                                Text(
                                  'Debug Info:',
                                  style: AppTypography.labelSmall.copyWith(
                                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                  ),
                                ),
                                const SizedBox(height: AppSpacing.xs),
                                Text(
                                  'Initialized: ${authState.isInitialized}',
                                  style: AppTypography.bodySmall.copyWith(
                                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                  ),
                                ),
                                Text(
                                  'Authenticated: ${authState.isAuthenticated}',
                                  style: AppTypography.bodySmall.copyWith(
                                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                  ),
                                ),
                                if (authState.user != null)
                                  Text(
                                    'User: ${authState.user?.name}',
                                    style: AppTypography.bodySmall.copyWith(
                                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                                    ),
                                  ),
                                if (authState.error != null)
                                  Text(
                                    'Error: ${authState.error}',
                                    style: AppTypography.bodySmall.copyWith(
                                      color: AppColors.danger,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  /// Responsive Logo Size
  double _getResponsiveLogoSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 100.0; // Small phones
    } else if (screenWidth < 600) {
      return 120.0; // Normal phones
    } else if (screenWidth < 900) {
      return 140.0; // Tablets
    } else {
      return 160.0; // Large screens
    }
  }

  /// Responsive Icon Size
  double _getResponsiveIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 50.0; // Small phones
    } else if (screenWidth < 600) {
      return 60.0; // Normal phones
    } else if (screenWidth < 900) {
      return 70.0; // Tablets
    } else {
      return 80.0; // Large screens
    }
  }

  /// Responsive Blur Radius
  double _getResponsiveBlurRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 15.0; // Small phones
    } else if (screenWidth < 600) {
      return 20.0; // Normal phones
    } else if (screenWidth < 900) {
      return 25.0; // Tablets
    } else {
      return 30.0; // Large screens
    }
  }

  /// Responsive Shadow Offset
  double _getResponsiveShadowOffset(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 8.0; // Small phones
    } else if (screenWidth < 600) {
      return 10.0; // Normal phones
    } else if (screenWidth < 900) {
      return 12.0; // Tablets
    } else {
      return 15.0; // Large screens
    }
  }

  /// Responsive Spacing
  double _getResponsiveSpacing(BuildContext context, String type) {
    final screenWidth = MediaQuery.of(context).size.width;

    switch (type) {
      case 'section':
        if (screenWidth < 360) {
          return 24.0; // Small phones
        } else if (screenWidth < 600) {
          return 32.0; // Normal phones
        } else if (screenWidth < 900) {
          return 40.0; // Tablets
        } else {
          return 48.0; // Large screens
        }
      case 'small':
        if (screenWidth < 360) {
          return 8.0; // Small phones
        } else if (screenWidth < 600) {
          return 12.0; // Normal phones
        } else if (screenWidth < 900) {
          return 16.0; // Tablets
        } else {
          return 20.0; // Large screens
        }
      default:
        return 16.0;
    }
  }

  /// Responsive Horizontal Padding
  double _getResponsiveHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 360) {
      return 16.0; // Small phones
    } else if (screenWidth < 600) {
      return 24.0; // Normal phones
    } else if (screenWidth < 900) {
      return 32.0; // Tablets
    } else {
      return 48.0; // Large screens
    }
  }

  /// Responsive Title Style
  TextStyle _getResponsiveTitleStyle(BuildContext context, ThemeData theme) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 360) {
      return AppTypography.h2.copyWith(
        color: theme.colorScheme.onSurface,
      );
    } else if (screenWidth < 600) {
      return AppTypography.appTitle.copyWith(
        color: theme.colorScheme.onSurface,
      );
    } else if (screenWidth < 900) {
      return AppTypography.appTitle.copyWith(
        color: theme.colorScheme.onSurface,
        fontSize: 36,
      );
    } else {
      return AppTypography.appTitle.copyWith(
        color: theme.colorScheme.onSurface,
        fontSize: 42,
      );
    }
  }

  /// Responsive Body Style
  TextStyle _getResponsiveBodyStyle(BuildContext context, ThemeData theme) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth < 360) {
      return AppTypography.bodyMedium.copyWith(
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      );
    } else if (screenWidth < 600) {
      return AppTypography.bodyLarge.copyWith(
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
      );
    } else if (screenWidth < 900) {
      return AppTypography.bodyLarge.copyWith(
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        fontSize: 18,
      );
    } else {
      return AppTypography.bodyLarge.copyWith(
        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
        fontSize: 20,
      );
    }
  }
}
