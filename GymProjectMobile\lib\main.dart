import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'core/core.dart';
import 'features/auth/presentation/providers/auth_provider.dart';

/// GymKod Pro Mobile App
///
/// Bu uygulama Angular frontend'deki tasarım sistemini takip eder.
/// Referans: GymProjectFrontend tasarım sistemi
///
/// Ana özellikler:
/// - JWT Token Authentication (Otomatik Token Refresh)
/// - QR Code sistemi
/// - Dark/Light theme support
/// - Riverpod state management
/// - Go Router navigation
/// - Production Ready

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // System UI ayarları (Angular'daki global styles'a benzer)
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.light,
    ),
  );

  // Desteklenen orientasyonlar
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Production mode'da service testleri kaldırıldı

  runApp(
    const ProviderScope(
      child: GymKodProApp(),
    ),
  );
}



class GymKodProApp extends ConsumerStatefulWidget {
  const GymKodProApp({super.key});

  @override
  ConsumerState<GymKodProApp> createState() => _GymKodProAppState();
}

class _GymKodProAppState extends ConsumerState<GymKodProApp> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    // Production'da lifecycle logları azaltıldı

    // Auth durumunu kontrol et - CRITICAL FIX
    switch (state) {
      case AppLifecycleState.resumed:
        // Uygulama foreground'a döndüğünde token durumunu kontrol et
        // Hem authenticated hem de unauthenticated durumda kontrol et
        ref.read(authProvider.notifier).checkAuthStatus();
        LoggingService.authLog('App resumed - checking auth status');
        break;
      case AppLifecycleState.paused:
        // TokenRefreshService background'da çalışmaya devam edecek
        LoggingService.authLog('App paused - token refresh continues in background');
        break;
      case AppLifecycleState.detached:
        // App tamamen kapatılıyor
        LoggingService.authLog('App detached');
        break;
      default:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final router = ref.watch(routerProvider);
    final themeMode = ref.watch(themeProvider); // Tema provider'dan tema modunu al

    // Auth state değişikliklerini dinle
    ref.listen<AuthState>(authProvider, (previous, next) {
      // Kullanıcı giriş yaptığında profil fotoğrafını refresh et
      if (previous?.isAuthenticated != next.isAuthenticated && next.isAuthenticated) {
        LoggingService.info('User logged in, refreshing profile image', tag: 'PROFILE_IMAGE');
        ref.read(profileImageProvider.notifier).forceRefreshProfileImage();
      }
    });

    return MaterialApp.router(
      // App Configuration
      title: AppConstants.appName,
      debugShowCheckedModeBanner: false,

      // Localization Configuration
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      supportedLocales: const [
        Locale('tr', 'TR'), // Türkçe
        Locale('en', 'US'), // İngilizce
      ],
      locale: const Locale('tr', 'TR'), // Varsayılan Türkçe

      // Theme Configuration (Angular frontend'deki theme system)
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: themeMode, // Provider'dan gelen tema modu

      // Router Configuration (Go Router)
      routerConfig: router,
    );
  }
}